
import { useState } from "react";
import { Check, AlertCircle } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { supabase } from "@/integrations/supabase/client";

// Define form schema with Zod
const formSchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  email: z.string().email("Invalid email address").min(1, { message: "Email is required" }),
  company: z.string().min(1, { message: "Organisation is required" }),
  job_title: z.string().min(1, { message: "Job title is required" }),
  linkedin_url: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  twitter_url: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  message: z.string().min(1, { message: "Message is required" })
});

type ContactFormValues = z.infer<typeof formSchema>;

const ContactSection = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  
  // Initialize form
  const form = useForm<ContactFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      job_title: "",
      linkedin_url: "",
      twitter_url: "",
      message: ""
    }
  });
  
  const onSubmit = async (data: ContactFormValues) => {
    setIsSubmitting(true);
    setSubmissionError(null);
    
    try {
      console.log("Submitting to Supabase:", data);
      
      const { error } = await supabase
        .from('contact_form')
        .insert([
          {
            name: data.name,
            email: data.email,
            company: data.company,
            job_title: data.job_title,
            linkedin_url: data.linkedin_url || null,
            twitter_url: data.twitter_url || null,
            message: data.message
          }
        ]);
      
      if (error) {
        console.error("Supabase error:", error);
        throw error;
      }
      
      // Success - via Supabase
      setIsSubmitting(false);
      setIsSubmitted(true);
      toast({
        title: "Message sent successfully",
        description: "We'll get back to you as soon as possible.",
      });
      
      // Reset form after some time
      setTimeout(() => {
        form.reset();
        setIsSubmitted(false);
      }, 5000);
      
    } catch (error) {
      console.error("Submission error:", error);
      setIsSubmitting(false);
      setSubmissionError("There was an error submitting your message. Please try again later.");
      
      toast({
        title: "Something went wrong",
        description: "Please try again later or contact us directly.",
        variant: "destructive"
      });
    }
  };

  return (
    <section id="contact" className="section-padding bg-gradient-to-b from-zenovate-lightgrey to-white">
      <div className="max-container">
        <div className="text-center mb-10">
          <h2 className="text-zenovate-charcoal mb-4 opacity-0 animate-fade-in">
            Partner with Zenovate
          </h2>
          
          <p className="text-gray-700 max-w-3xl mx-auto mb-8 opacity-0 animate-fade-in animate-delay-200">
            At Zenovate, we are committed to translating strategic vision into tangible, impactful outcomes. Our approach is collaborative, drawing on extensive experience and a strong network across public, private, and voluntary sectors.
          </p>
          
          <p className="text-lg font-medium text-zenovate-teal mb-8 opacity-0 animate-fade-in animate-delay-300">
            Ready to explore how Zenovate can empower your organisation?
          </p>
        </div>
        
        {submissionError && (
          <Alert variant="destructive" className="max-w-2xl mx-auto mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{submissionError}</AlertDescription>
          </Alert>
        )}
        
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8 opacity-0 animate-fade-in animate-delay-400">
          {isSubmitted ? (
            <div className="text-center py-8">
              <div className="bg-green-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-zenovate-teal mb-2">
                Message Received!
              </h3>
              <p className="text-gray-700">
                Thank you for reaching out. We'll get back to you as soon as possible.
              </p>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-zenovate-teal focus:border-transparent" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="email"
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-zenovate-teal focus:border-transparent" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Organisation *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-zenovate-teal focus:border-transparent" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="job_title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-zenovate-teal focus:border-transparent" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="linkedin_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>LinkedIn URL (Optional)</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="https://linkedin.com/in/yourprofile"
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-zenovate-teal focus:border-transparent" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="twitter_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>X/Twitter URL (Optional)</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          placeholder="https://x.com/yourusername"
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-zenovate-teal focus:border-transparent" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Your Message *</FormLabel>
                      <FormControl>
                        <Textarea 
                          {...field} 
                          rows={5}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-zenovate-teal focus:border-transparent" 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button
                  type="submit"
                  className={`w-full btn-primary flex items-center justify-center ${
                    isSubmitting ? 'opacity-80 cursor-not-allowed' : ''
                  }`}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </form>
            </Form>
          )}
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
