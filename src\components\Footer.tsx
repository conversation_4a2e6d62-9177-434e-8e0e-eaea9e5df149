
import { Mail, Linkedin } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-zenovate-charcoal text-white py-12">
      <div className="max-container">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-6 md:mb-0">
            <h3 className="text-2xl font-bold mb-2">Zenovate</h3>
            <p className="text-gray-300">Innovation with Purpose</p>
          </div>
          
          <div className="flex flex-col items-center md:items-end">
            <div className="flex space-x-4 mb-4">
              <a 
                href="mailto:<EMAIL>"
                className="text-white hover:text-zenovate-green transition-colors"
                aria-label="Email us"
              >
                <Mail className="h-5 w-5" />
              </a>
              <a 
                href="https://linkedin.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-zenovate-green transition-colors"
                aria-label="LinkedIn profile"
              >
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
            
            <p className="text-sm text-gray-400">
              &copy; {currentYear} Zenovate. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
