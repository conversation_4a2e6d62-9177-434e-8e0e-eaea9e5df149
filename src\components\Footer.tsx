
import { Mail, ExternalLink } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-zenovate-charcoal text-white py-16">
      <div className="max-container">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div className="md:col-span-2">
            <h3 className="text-2xl font-bold mb-4">ZENOVATE LTD</h3>
            <p className="text-gray-300 mb-4">Innovation with Purpose</p>
            <div className="text-sm text-gray-400 space-y-1">
              <p>Company No: 16114916</p>
              <p>Incorporated: 3 December 2024</p>
              <p>124-128 City Road, London, England, EC1V 2NX</p>
              <p>
                Contact: <a
                  href="mailto:<EMAIL>"
                  className="text-zenovate-green hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>

          {/* Legal Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Legal</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  to="/privacy-policy"
                  className="text-gray-300 hover:text-zenovate-green transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  to="/terms-of-service"
                  className="text-gray-300 hover:text-zenovate-green transition-colors"
                >
                  Terms of Service
                </Link>
              </li>
              <li>
                <button className="text-gray-300 hover:text-zenovate-green transition-colors">
                  Cookie Settings
                </button>
              </li>
            </ul>
          </div>

          {/* Social Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Follow Us</h4>
            <div className="flex space-x-4">
              <a
                href="mailto:<EMAIL>"
                className="text-white hover:text-zenovate-green transition-colors"
                aria-label="Email us"
              >
                <Mail className="h-5 w-5" />
              </a>
              <a
                href="https://www.linkedin.com/company/*********/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-zenovate-green transition-colors flex items-center gap-1"
                aria-label="LinkedIn profile"
              >
                <ExternalLink className="h-4 w-4" />
                <span className="text-sm">LinkedIn</span>
              </a>
              <a
                href="https://x.com/ZenovateUK"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-zenovate-green transition-colors flex items-center gap-1"
                aria-label="Twitter/X profile"
              >
                <ExternalLink className="h-4 w-4" />
                <span className="text-sm">X</span>
              </a>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-600 pt-8">
          <p className="text-sm text-gray-400 text-center">
            &copy; {currentYear} Zenovate. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
