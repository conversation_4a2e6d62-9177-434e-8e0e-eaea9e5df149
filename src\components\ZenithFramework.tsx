
import { useState } from "react";
import { ArrowRight, Leaf, Lightbulb, Cpu, Heart, TrendingUp, Users } from "lucide-react";

// Define the framework item type
interface FrameworkItem {
  letter: string;
  title: string;
  whatItIs: string;
  whyNeeded: string;
  icon: React.ReactNode;
}

const ZenithFramework = () => {
  const [activeItem, setActiveItem] = useState<string | null>("Z");
  
  // Define the ZENITH framework items
  const frameworkItems: FrameworkItem[] = [
    {
      letter: "Z",
      title: "Zero Carbon Future & Sustainability",
      whatItIs: "Embedding environmental responsibility into your core operations with robust Net Zero strategies and sustainable innovation.",
      whyNeeded: "A commitment to sustainability enhances brand reputation, attracts eco-conscious talent and customers, drives operational efficiencies, and unlocks new market opportunities in the green economy.",
      icon: <Leaf className="h-10 w-10 text-white" />
    },
    {
      letter: "E",
      title: "Evolution through Purposeful Innovation",
      whatItIs: "Fostering a culture of continuous improvement and forward-thinking aligned with your core purpose and values.",
      whyNeeded: "Purposeful innovation ensures you stay ahead of competition, meet changing customer demands, improve internal processes, and build a resilient organization.",
      icon: <Lightbulb className="h-10 w-10 text-white" />
    },
    {
      letter: "N",
      title: "Next-Generation AI & Digital Transformation",
      whatItIs: "Leveraging AI and cutting-edge digital technologies to revolutionize operations, customer engagement, and decision-making.",
      whyNeeded: "AI and digital tools unlock operational efficiencies, provide data-driven insights, enable personalized experiences, and identify new revenue streams.",
      icon: <Cpu className="h-10 w-10 text-white" />
    },
    {
      letter: "I",
      title: "Impactful Human & Planetary Health",
      whatItIs: "Integrating initiatives that support physical and mental wellness, community engagement, and environmental stewardship.",
      whyNeeded: "A focus on holistic health creates engaged employees, builds brand loyalty, attracts top talent, and fosters a positive organizational culture.",
      icon: <Heart className="h-10 w-10 text-white" />
    },
    {
      letter: "T",
      title: "Transformative Growth & Market Leadership",
      whatItIs: "Achieving significant growth by strategically positioning your business with a refined value proposition and strong competitive advantage.",
      whyNeeded: "We help you identify opportunities, overcome barriers, and develop strategies that increase market share and solidify your reputation as an industry leader.",
      icon: <TrendingUp className="h-10 w-10 text-white" />
    },
    {
      letter: "H",
      title: "Holistic Strategy & Stakeholder Value",
      whatItIs: "Developing comprehensive strategies that consider all aspects of your business and its impact on all stakeholders.",
      whyNeeded: "A holistic approach builds stronger relationships, enhances loyalty, mitigates risks, and creates a sustainable business model valued by all stakeholders.",
      icon: <Users className="h-10 w-10 text-white" />
    }
  ];
  
  // Function to get color based on letter - updated with new color palette
  const getColorClass = (letter: string) => {
    switch(letter) {
      case 'Z': return 'bg-[#264653]';  // Charcoal
      case 'E': return 'bg-[#2a9d8f]';  // Persian green
      case 'N': return 'bg-[#e9c46a]';  // Saffron
      case 'I': return 'bg-[#f4a261]';  // Sandy brown
      case 'T': return 'bg-[#e76f51]';  // Burnt sienna
      case 'H': return 'bg-[#e97c61]';  // Burnt sienna 2
      default: return 'bg-[#264653]';
    }
  };

  // Function to get text color based on letter - updated for new color palette
  const getTextColorClass = (letter: string) => {
    switch(letter) {
      case 'Z': return 'text-[#264653]';  // Charcoal
      case 'E': return 'text-[#2a9d8f]';  // Persian green
      case 'N': return 'text-[#e9c46a]';  // Saffron
      case 'I': return 'text-[#f4a261]';  // Sandy brown
      case 'T': return 'text-[#e76f51]';  // Burnt sienna
      case 'H': return 'text-[#e97c61]';  // Burnt sienna 2
      default: return 'text-[#264653]';
    }
  };

  // Format the title to color only the first letter of the title that matches the framework letter
  const formatTitle = (letter: string, title: string) => {
    // Split the title into words
    const words = title.split(' ');
    
    return words.map((word, index) => {
      // First letter of the word
      const firstLetter = word.charAt(0);
      
      // If this is the first word that starts with our framework letter
      if (firstLetter.toUpperCase() === letter.toUpperCase()) {
        return (
          <span key={index} className="whitespace-normal">
            <span className={getTextColorClass(letter)}>{firstLetter}</span>
            {word.substring(1)}
            {index < words.length - 1 ? ' ' : ''}
          </span>
        );
      } else {
        return <span key={index} className="whitespace-normal">{word}{index < words.length - 1 ? ' ' : ''}</span>;
      }
    });
  };

  return (
    <section id="framework" className="section-padding bg-gradient-to-b from-white to-gray-50">
      <div className="max-container">
        <div className="text-center mb-16">
          <h2 className="text-zenovate-charcoal mb-4 opacity-0 animate-fade-in">
            The ZENITH Framework: Guiding Your Ascent to Purposeful Growth
          </h2>
          
          <p className="text-gray-700 max-w-3xl mx-auto opacity-0 animate-fade-in animate-delay-200">
            Our proprietary ZENITH Framework guides your organisation through a transformative journey, integrating cutting-edge strategies with a commitment to positive impact.
          </p>
        </div>

        {/* ZENITH Visual Interactive Element */}
        <div className="mb-16 flex flex-wrap justify-center gap-3 md:gap-4">
          {frameworkItems.map((item, index) => (
            <button
              key={item.letter}
              onClick={() => setActiveItem(item.letter)}
              className={`
                w-16 h-16 md:w-20 md:h-20 rounded-full flex items-center justify-center
                transition-all duration-300 opacity-0 animate-scale-in shadow-lg
                ${getColorClass(item.letter)}
                ${activeItem === item.letter ? 'ring-4 ring-offset-4 ring-offset-gray-50 ring-white scale-110' : 'hover:opacity-90 hover:scale-105 hover:shadow-xl cursor-pointer'}
              `}
              style={{ animationDelay: `${100 * index}ms` }}
              aria-label={`Select ${item.title}`}
            >
              <span className="text-3xl font-bold text-white">{item.letter}</span>
            </button>
          ))}
        </div>

        {/* Active Item Details */}
        {activeItem && (
          <div className="bg-white rounded-lg shadow-xl p-6 md:p-8 max-w-4xl mx-auto transition-all duration-500 card-hover border border-gray-100">
            {frameworkItems
              .filter(item => item.letter === activeItem)
              .map(item => (
                <div key={item.letter} className="animate-fade-in">
                  <div className="flex flex-col md:flex-row md:items-start gap-4 md:gap-6 mb-6">
                    <div className={`rounded-full w-16 h-16 flex items-center justify-center flex-shrink-0 ${getColorClass(item.letter)} mx-auto md:mx-0`}>
                      {item.icon}
                    </div>
                    <div>
                      <div className="flex flex-col md:flex-row md:items-center gap-1 md:gap-3 mb-1 text-center md:text-left">
                        <span className={`text-4xl md:text-5xl font-bold ${getTextColorClass(item.letter)}`}>{item.letter}</span>
                        <h3 className="text-zenovate-charcoal text-xl md:text-2xl lg:text-3xl break-words">
                          {formatTitle(item.letter, item.title)}
                        </h3>
                      </div>
                      <div className="h-1 w-16 mb-4 rounded-full mx-auto md:mx-0" style={{ backgroundColor: getTextColorClass(item.letter).replace('text-', '#').replace('[', '').replace(']', '') }}></div>
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold mb-2 text-gray-800">What it is:</h4>
                    <p className="text-gray-700">{item.whatItIs}</p>
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-semibold mb-2 text-gray-800">Why your business needs this:</h4>
                    <p className="text-gray-700">{item.whyNeeded}</p>
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default ZenithFramework;
