
import { useState } from "react";
import { <PERSON>Down, Circle, CircleCheck } from "lucide-react";

const HeroSection = () => {
  const [hovered, setHovered] = useState<number | null>(null);
  
  const pillars = [
    "Sustainability",
    "Digital Transformation",
    "Human & Planetary Health"
  ];
  
  const scrollToFramework = () => {
    const frameworkSection = document.getElementById("framework");
    if (frameworkSection) {
      frameworkSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section className="relative min-h-[85vh] flex items-center">
      {/* Background Image - UK countryside */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-black/40 to-black/60 z-10"></div>
        <img 
          src="https://images.unsplash.com/photo-1469474968028-56623f02e42e" 
          alt="UK countryside" 
          className="w-full h-full object-cover object-center"
        />
      </div>

      {/* Subtle Background Elements */}
      <div className="absolute inset-0 overflow-hidden z-20">
        <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-[#2a9d8f] opacity-5 blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-80 h-80 rounded-full bg-[#e9c46a] opacity-5 blur-3xl"></div>
        <div className="absolute top-1/3 left-1/2 w-72 h-72 rounded-full bg-[#e76f51] opacity-5 blur-3xl"></div>
      </div>

      <div className="max-container relative z-30">
        <div className="flex flex-col items-center text-center">
          <div className="mb-6 flex flex-col items-center justify-center">
            <img 
              src="/lovable-uploads/2d74c6df-6334-4d01-b276-5d49fd37d12f.png" 
              alt="Zenovate Logo" 
              className="h-24 w-24 mb-4" 
            />
            <h1 className="text-5xl md:text-7xl lg:text-8xl mb-2 opacity-0 animate-fade-in relative font-bold tracking-tight">
              <span className="text-white">Zeno</span><span className="text-[#e76f51]">vate</span>
              <div className="absolute inset-0 bg-black/0 pointer-events-none"></div>
            </h1>
          </div>
          
          <h2 className="text-2xl md:text-3xl mb-8 max-w-2xl mx-auto opacity-0 animate-fade-in animate-delay-200">
            <span className="text-white">Innovation isn't just about progress</span>
            <span className="text-[#e76f51]">, it's about purpose.</span>
          </h2>
          
          <div className="flex flex-wrap justify-center gap-3 mb-10 opacity-0 animate-fade-in animate-delay-300">
            {pillars.map((pillar, index) => (
              <div 
                key={pillar}
                className="relative cursor-pointer transition-all duration-300"
                onMouseEnter={() => setHovered(index)}
                onMouseLeave={() => setHovered(null)}
              >
                <div className={`px-4 py-2 rounded-full transition-all duration-300 ${
                  hovered === index 
                    ? "bg-[#2a9d8f] text-white transform scale-105" 
                    : "bg-white text-[#264653] border border-[#2a9d8f]"
                }`}>
                  <span className="flex items-center gap-2">
                    {hovered === index ? <CircleCheck size={16} /> : <Circle size={16} />}
                    {pillar}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          <button 
            onClick={scrollToFramework}
            className="bg-[#264653] hover:bg-[#2a9d8f] text-white font-medium py-3 px-6 rounded-md transition-all duration-300 inline-block opacity-0 animate-fade-in animate-delay-400 shadow-sm"
          >
            Discover Our Framework
          </button>
          
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
            <ArrowDown className="w-6 h-6 text-white" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
