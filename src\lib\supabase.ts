
import { createClient } from '@supabase/supabase-js';

// Get environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Debug logging to help diagnose the issue
console.log("Supabase URL available:", !!supabaseUrl);
console.log("Supabase Key available:", !!supabaseKey);

// Check if required environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check .env file.');
}

// Create supabase client with proper error handling
export const supabaseClient = createClient(
  supabaseUrl || '',  // Fallback to empty string if undefined
  supabaseKey || '',  // Fallback to empty string if undefined
  {
    auth: {
      persistSession: true
    }
  }
);

// Add a helper function to check if Supabase client is properly configured
export const isSupabaseConfigured = () => {
  return !!supabaseUrl && !!supabaseKey;
};
