
import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 60) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    setIsMenuOpen(false);
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <nav
      className={`fixed w-full z-50 transition-all duration-300 ${
        scrolled ? "py-2 bg-white/95 backdrop-blur-sm shadow-md" : "py-4 bg-transparent"
      }`}
    >
      <div className="max-container flex justify-between items-center">
        <a 
          href="#" 
          className="flex items-center"
          onClick={(e) => {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: "smooth" });
          }}
        >
          <img 
            src="/lovable-uploads/2d74c6df-6334-4d01-b276-5d49fd37d12f.png" 
            alt="Zenovate Logo" 
            className="h-10 w-auto" 
          />
        </a>

        {/* Desktop Navigation */}
        <div className="hidden md:flex space-x-8">
          <button 
            onClick={() => scrollToSection("framework")}
            className={`${scrolled ? 'text-[#264653]' : 'text-white'} hover:text-[#2a9d8f] transition-colors focus-ring font-medium`}
          >
            Framework
          </button>
          <button 
            onClick={() => scrollToSection("services")}
            className={`${scrolled ? 'text-[#264653]' : 'text-white'} hover:text-[#2a9d8f] transition-colors focus-ring font-medium`}
          >
            Services
          </button>
          <button 
            onClick={() => scrollToSection("about")}
            className={`${scrolled ? 'text-[#264653]' : 'text-white'} hover:text-[#2a9d8f] transition-colors focus-ring font-medium`}
          >
            About
          </button>
          <button 
            onClick={() => scrollToSection("contact")}
            className={`${scrolled ? 'text-[#264653]' : 'text-white'} hover:text-[#2a9d8f] transition-colors focus-ring font-medium`}
          >
            Contact
          </button>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="focus:outline-none focus:ring-2 focus:ring-[#2a9d8f] p-2 rounded-md"
          >
            {isMenuOpen ? (
              <X className={`h-6 w-6 ${scrolled ? 'text-[#264653]' : 'text-white'}`} />
            ) : (
              <Menu className={`h-6 w-6 ${scrolled ? 'text-[#264653]' : 'text-white'}`} />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white shadow-lg py-4 px-6 animate-slide-up">
          <div className="flex flex-col space-y-4">
            <button 
              onClick={() => scrollToSection("framework")}
              className="text-[#264653] hover:text-[#2a9d8f] transition-colors py-2 font-medium"
            >
              Framework
            </button>
            <button 
              onClick={() => scrollToSection("services")}
              className="text-[#264653] hover:text-[#2a9d8f] transition-colors py-2 font-medium"
            >
              Services
            </button>
            <button 
              onClick={() => scrollToSection("about")}
              className="text-[#264653] hover:text-[#2a9d8f] transition-colors py-2 font-medium"
            >
              About
            </button>
            <button 
              onClick={() => scrollToSection("contact")}
              className="text-[#264653] hover:text-[#2a9d8f] transition-colors py-2 font-medium"
            >
              Contact
            </button>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
