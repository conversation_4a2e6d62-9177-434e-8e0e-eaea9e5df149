
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 187 100% 24%;
    --primary-foreground: 210 40% 98%;

    --secondary: 187 37% 64%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 262 58% 57%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 187 100% 24%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-opensans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-montserrat font-bold;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }
  
  .animate-delay-100 {
    animation-delay: 100ms;
  }
  
  .animate-delay-200 {
    animation-delay: 200ms;
  }
  
  .animate-delay-300 {
    animation-delay: 300ms;
  }
  
  .animate-delay-400 {
    animation-delay: 400ms;
  }
  
  .animate-delay-500 {
    animation-delay: 500ms;
  }
  
  .animate-delay-600 {
    animation-delay: 600ms;
  }
  
  .animate-delay-700 {
    animation-delay: 700ms;
  }
  
  .animate-delay-800 {
    animation-delay: 800ms;
  }

  .shadow-hover {
    @apply transition-all duration-300 hover:shadow-lg;
  }
  
  .section-padding {
    @apply py-16 md:py-24;
  }
  
  .max-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .btn-primary {
    @apply bg-zenovate-teal hover:bg-opacity-90 text-white font-semibold py-3 px-6 rounded-md transition-all duration-300 inline-block;
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:translate-y-[-5px] hover:shadow-lg;
  }
  
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-zenovate-teal focus:ring-opacity-50;
  }
}
