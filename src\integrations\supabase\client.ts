// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://lymezbofypeksoltuxqs.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5bWV6Ym9meXBla3NvbHR1eHFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5MTUxOTQsImV4cCI6MjA2MzQ5MTE5NH0.5zuIdreewSyFHldSpDEuvSLj9pfc4cLsFl_wFsxK98w";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);