
import { useState, useEffect } from "react";
import { Chevron<PERSON>eft, ChevronRight, Quote } from "lucide-react";

interface Testimonial {
  id: number;
  text: string;
  industry?: string;
}

const TestimonialsSection = () => {
  const testimonials: Testimonial[] = [
    {
      id: 1,
      text: "Zenovate revolutionized our approach to sustainability, helping us achieve our Net Zero targets while improving operational efficiency by 30% across our healthcare services.",
      industry: "Healthcare"
    },
    {
      id: 2,
      text: "The ZENITH framework transformed our digital strategy, enabling us to leverage AI technologies that delivered measurable impact for both our business and environmental initiatives.",
      industry: "Technology"
    },
    {
      id: 3,
      text: "Partnering with Zenovate helped us navigate complex procurement challenges while embedding sustainability principles that resonated with our stakeholders and improved our market position.",
      industry: "Construction"
    },
    {
      id: 4,
      text: "The strategic innovation expertise from Zenovate enabled our team to develop groundbreaking solutions that address climate challenges while opening new revenue streams we hadn't previously considered.",
      industry: "Energy"
    },
    {
      id: 5,
      text: "Through Zenovate's guidance, we implemented purposeful digital transformation that enhanced our competitive advantage and positioned us as leaders in sustainable retail practices.",
      industry: "Retail"
    },
    {
      id: 6,
      text: "Zenovate's holistic approach to innovation helped us create lasting value for all stakeholders while significantly reducing our environmental impact across our housing developments.",
      industry: "Housing"
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // Determine number of testimonials to show based on screen width
  const getTestimonialsToShow = () => {
    if (windowWidth < 768) return 1;
    if (windowWidth < 1024) return 2;
    return 3;
  };

  const testimonialsPerView = getTestimonialsToShow();
  const maxIndex = testimonials.length - testimonialsPerView;

  const nextTestimonial = () => {
    setCurrentIndex(prevIndex => 
      prevIndex >= maxIndex ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex(prevIndex => 
      prevIndex <= 0 ? maxIndex : prevIndex - 1
    );
  };

  const visibleTestimonials = testimonials.slice(
    currentIndex,
    currentIndex + testimonialsPerView
  );

  return (
    <section className="section-padding bg-white">
      <div className="max-container">
        <div className="text-center mb-16">
          <h2 className="text-zenovate-charcoal mb-4 opacity-0 animate-fade-in">
            Client Success Stories
          </h2>
        </div>

        <div className="relative">
          {/* Navigation buttons */}
          <div className="absolute top-1/2 -left-4 md:-left-6 transform -translate-y-1/2 z-10">
            <button 
              onClick={prevTestimonial} 
              className="bg-white rounded-full p-2 shadow-lg text-zenovate-teal hover:bg-zenovate-teal hover:text-white transition-colors"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-6 w-6" />
            </button>
          </div>
          
          <div className="absolute top-1/2 -right-4 md:-right-6 transform -translate-y-1/2 z-10">
            <button 
              onClick={nextTestimonial} 
              className="bg-white rounded-full p-2 shadow-lg text-zenovate-teal hover:bg-zenovate-teal hover:text-white transition-colors"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-6 w-6" />
            </button>
          </div>
          
          {/* Testimonials carousel */}
          <div className="flex gap-6 overflow-hidden px-4">
            {visibleTestimonials.map((testimonial, index) => (
              <div 
                key={`visible-${testimonial.id}`}
                className="flex-1 bg-zenovate-lightgrey rounded-lg p-6 shadow-md card-hover opacity-0 animate-fade-in relative"
                style={{ animationDelay: `${150 * index}ms` }}
              >
                <Quote className="absolute top-4 right-4 h-8 w-8 text-zenovate-teal opacity-20" />
                
                <div className="mb-6">
                  <p className="text-gray-700 italic leading-relaxed">
                    "{testimonial.text}"
                  </p>
                </div>
                
                <div>
                  {testimonial.industry && (
                    <p className="text-sm font-medium text-zenovate-teal">{testimonial.industry} Sector</p>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {/* Pagination indicators */}
          <div className="flex justify-center mt-6 gap-2">
            {Array.from({ length: maxIndex + 1 }).map((_, index) => (
              <button
                key={`indicator-${index}`}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex ? 'bg-zenovate-teal w-6' : 'bg-gray-300'
                }`}
                aria-label={`Go to testimonial set ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
