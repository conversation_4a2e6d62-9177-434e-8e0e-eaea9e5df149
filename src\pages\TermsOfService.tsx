import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const TermsOfService = () => {
  return (
    <main className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <Navbar />
      <div className="max-container py-20">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-zenovate-charcoal mb-8">Terms of Service</h1>
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-600 mb-6">
              Last updated: {new Date().toLocaleDateString()}
            </p>
            
            <div className="bg-white p-8 rounded-lg shadow-sm border">
              <p className="text-gray-700 mb-4">
                This Terms of Service page is currently being updated with detailed information about the terms and conditions for using Zenovate Ltd's services.
              </p>
              
              <p className="text-gray-700 mb-4">
                Please check back soon for our complete Terms of Service.
              </p>
              
              <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-semibold text-zenovate-charcoal mb-2">Contact Information</h3>
                <p className="text-gray-700">
                  <strong>Zenovate Ltd</strong><br />
                  Company No: 16114916<br />
                  124-128 City Road, London, England, EC1V 2NX<br />
                  Email: <a href="mailto:<EMAIL>" className="text-zenovate-green hover:underline"><EMAIL></a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </main>
  );
};

export default TermsOfService;
