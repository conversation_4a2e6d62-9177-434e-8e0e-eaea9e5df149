
import { Check, Activity, Users, Globe, ShoppingCart, Leaf, Code } from "lucide-react";

// Define the service item type
interface ServiceItem {
  title: string;
  whatWeDo: string;
  whyNeeded: string;
  icon: React.ReactNode;
}

const ServicesSection = () => {
  // Define services
  const services: ServiceItem[] = [
    {
      title: "Strategic Project & Programme Management",
      whatWeDo: "We provide expert leadership for your most critical projects, from initial concept and planning through to successful delivery and impact assessment. This includes managing major development initiatives, organisational transformations, and system-wide improvements.",
      whyNeeded: "Ensure your key initiatives are delivered on time, within budget, and achieve their intended strategic outcomes, minimizing risk and maximizing ROI.",
      icon: <Activity className="h-10 w-10 text-[#2a9d8f]" />
    },
    {
      title: "High-Performance Team Development",
      whatWeDo: "We work with your teams to enhance collaboration, build essential skills, and foster a culture of innovation and continuous improvement.",
      whyNeeded: "Empowered and cohesive teams are more agile, productive, and innovative, driving better business results and fostering a positive work environment.",
      icon: <Users className="h-10 w-10 text-[#2a9d8f]" />
    },
    {
      title: "Business Support & SME Empowerment",
      whatWeDo: "We assist businesses, particularly SMEs, in refining their value propositions, identifying market opportunities, and developing growth strategies. Our support includes overcoming challenges, leveraging innovation, accessing funding opportunities, and scaling operations effectively.",
      whyNeeded: "Navigate complex market dynamics effectively, unlock your full business potential with expert guidance to compete more effectively, innovate rapidly, and achieve sustainable growth.",
      icon: <Globe className="h-10 w-10 text-[#2a9d8f]" />
    },
    {
      title: "Strategic Procurement and Commissioning",
      whatWeDo: "Our team offers deep expertise in managing complex procurement processes and commissioning services, ensuring value for money, robust contract management, and alignment with strategic goals.",
      whyNeeded: "Optimise your procurement spend, secure reliable and high-quality services, and ensure your commissioning activities deliver maximum impact and efficiency.",
      icon: <ShoppingCart className="h-10 w-10 text-[#2a9d8f]" />
    },
    {
      title: "Net Zero & Environmental Sustainability Solutions",
      whatWeDo: "We provide comprehensive support for your journey to Net Zero and beyond. This includes carbon footprint analysis, sustainability strategy development, guidance on regulatory compliance, and identifying opportunities for green innovation.",
      whyNeeded: "Meet increasing stakeholder expectations, reduce environmental impact, identify cost savings, enhance brand reputation, and ensure long-term business resilience in a changing world.",
      icon: <Leaf className="h-10 w-10 text-[#2a9d8f]" />
    },
    {
      title: "AI & Digital Transformation for Future-Readiness",
      whatWeDo: "We help businesses understand, adopt, and maximise the benefits of Artificial Intelligence and digital technologies. This includes identifying use cases, developing implementation roadmaps, and supporting change management to drive growth and efficiency.",
      whyNeeded: "Stay ahead of the curve by leveraging cutting-edge technologies to streamline operations, enhance customer experiences, unlock new revenue streams, and make smarter, data-driven decisions.",
      icon: <Code className="h-10 w-10 text-[#2a9d8f]" />
    }
  ];

  return (
    <section id="services" className="section-padding bg-gradient-to-r from-white to-gray-50">
      <div className="max-container">
        <div className="text-center mb-16">
          <h2 className="text-[#264653] mb-4 opacity-0 animate-fade-in">
            Our Comprehensive Service Offerings
          </h2>
          
          <p className="text-[#264653] max-w-3xl mx-auto opacity-0 animate-fade-in animate-delay-200">
            Zenovate offers a suite of services designed to empower your organisation to achieve its strategic objectives with purpose and impact:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div 
              key={service.title}
              className="bg-white rounded-lg p-6 shadow-md card-hover opacity-0 animate-fade-in border-l-4 border-[#2a9d8f]"
              style={{ animationDelay: `${150 * index}ms` }}
            >
              <div className="mb-4 flex items-center gap-4">
                <div className="bg-[#f4a261] bg-opacity-10 p-3 rounded-full shadow-md">
                  {service.icon}
                </div>
                <h3 className="text-xl font-semibold text-[#264653]">{service.title}</h3>
              </div>
              
              <div className="mb-4">
                <h4 className="font-semibold mb-2 text-[#2a9d8f]">What we do:</h4>
                <p className="text-[#264653]">{service.whatWeDo}</p>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2 text-[#2a9d8f]">Why your business needs this:</h4>
                <p className="text-[#264653]">{service.whyNeeded}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
